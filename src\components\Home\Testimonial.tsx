import React from "react";
import useEmblaCarousel from "embla-carousel-react";
import { ArrowLeft, ArrowRight } from "lucide-react";

const testimonials = [
	{
		id: 1,
		title: "Reliable, Scalable, and Adaptable",
		content:
			"I confidently recommend Migranium to any organization seeking a reliable, scalable, and adaptable solution. It's robust design, ease of use, and excellent support have been critical in enhancing our service delivery.",
		author: "Tiffany Budhoo",
		role: "System Planner",
		company: "Toronto Hydro",
		logo: "/assets/images/NWT.svg",
	},
	{
		id: 2,
		title: "Effortless Patient Management",
		content:
			"As a doctor managing numerous patients, I've finally found the solution in Migranium. It's streamlined our patient management and the analytics are invaluable for resource planning and enhancing care efficiency.",
		author: "Dr. <PERSON>",
		role: "Physician",
		company: "Network Medicals",
		logo: "/assets/images/NM.svg",
	},
	{
		id: 3,
		title: "We Now Work More Efficiently!",
		content:
			"Migranium has streamlined our busy clinic's workflow. With over 70 patients daily, reducing wait times was crucial. Now, our patients are happier, and we can work more efficiently. It's been a real game-changer!",
		author: "<PERSON><PERSON>",
		role: "Clinic Administrator",
		company: "Healthcare Solutions",
		logo: "",
	},
	{
		id: 4,
		title: "Smarter Visits with Migranium",
		content:
			"Migranium has significantly improved my clinic visits experience. Before, I faced long, uncertain waits; now, I can see exactly when it's my turn and can use my time productively. I'm very happy with this!",
		author: "Ghazela Amran",
		role: "Patient",
		company: "MedTech Corp",
		logo: "",
	},
];

export const Testimonial = () => {
	const [emblaRef, emblaApi] = useEmblaCarousel({
		slidesToScroll: 1,
		align: "start",
		loop: false,
		containScroll: "trimSnaps",
		dragFree: true,
		breakpoints: {
			"(min-width: 768px)": {
				align: "start",
				containScroll: "trimSnaps",
			},
			"(min-width: 1024px)": {
				align: "start",
				containScroll: "trimSnaps",
			},
		},
	});

	const [prevBtnDisabled, setPrevBtnDisabled] = React.useState(false);
	const [nextBtnDisabled, setNextBtnDisabled] = React.useState(false);

	const scrollPrev = React.useCallback(() => {
		if (emblaApi) emblaApi.scrollPrev();
	}, [emblaApi]);

	const scrollNext = React.useCallback(() => {
		if (emblaApi) emblaApi.scrollNext();
	}, [emblaApi]);

	const onSelect = React.useCallback(() => {
		if (!emblaApi) return;
		setPrevBtnDisabled(!emblaApi.canScrollPrev());
		setNextBtnDisabled(!emblaApi.canScrollNext());
	}, [emblaApi]);

	React.useEffect(() => {
		if (!emblaApi) return;
		onSelect();
		emblaApi.on("select", onSelect);
		emblaApi.on("reInit", onSelect);

		return () => {
			emblaApi.off("select", onSelect);
			emblaApi.off("reInit", onSelect);
		};
	}, [emblaApi, onSelect]);

	return (
		<section className="py-16 font-inter md:py-24">
			<div className="mx-auto max-w-7xl">
				<div className="msd:max-w-[312px] mx-auto mb-12 w-full text-center">
					<span className="mb-2 block font-shantell text-sm font-medium text-[#01B18B] lg:text-lg">
						Testimonials
					</span>
					<h2 className="mb-3 text-xl font-bold text-[#0C0D0E] lg:text-4xl">
						Why Our Users Love Us
					</h2>
					<p className="mx-auto max-w-2xl text-sm text-[#68778D] lg:text-lg">
						Every feature is shaped by the people who use it—hear
						what they love about Migranium.
					</p>
				</div>

				<div className="relative overflow-hidden" ref={emblaRef}>
					<div className="flex">
						{testimonials.map((testimonial) => (
							<div
								key={testimonial.id}
								className="w-full flex-[0_0_100%] px-4 md:flex-[0_0_45%] lg:flex-[0_0_32.5%]"
							>
								<div className="h-full rounded-[12px] border border-[#E4E4E7] p-6 shadow-[0px_1px_2px_0px_#0000000D]">
									<h3 className="mb-4 text-base font-semibold text-[#18181B] lg:text-lg">
										{testimonial.title}
									</h3>

									<p className="mb-8 text-sm leading-relaxed text-[#18181B] lg:text-base">
										{testimonial.content}
									</p>

									<div className="flex items-center justify-between">
										<div>
											<h4 className="text-sm font-medium text-[#18181B] lg:text-base">
												{testimonial.author}
											</h4>
											<p className="text-sm text-[#71717A]">
												{testimonial.role}
											</p>
										</div>
										{testimonial.logo && (
											<div className="flex-shrink-0">
												<img
													src={testimonial.logo}
													alt={testimonial.company}
													className="h-6 w-[66px] object-contain"
												/>
											</div>
										)}
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
				<div className="mt-8 flex justify-center gap-12">
					<button
						onClick={scrollPrev}
						disabled={prevBtnDisabled}
						className={`flex h-10 w-10 items-center justify-center rounded-full transition-all ${
							prevBtnDisabled
								? "cursor-not-allowed bg-[#E8EBEE]/30 text-[#0C0D0E]/30"
								: "bg-[#E8EBEE] text-[#0C0D0E] hover:bg-[#D8DDE2]"
						}`}
						aria-label="Previous testimonial"
					>
						<ArrowLeft className="h-4 w-4" />
					</button>

					<button
						onClick={scrollNext}
						disabled={nextBtnDisabled}
						className={`flex h-10 w-10 items-center justify-center rounded-full transition-all ${
							nextBtnDisabled
								? "cursor-not-allowed text-gray-300 opacity-30"
								: "bg-[#E8EBEE] text-[#0C0D0E] hover:bg-[#D8DDE2]"
						}`}
						aria-label="Next testimonial"
					>
						<ArrowRight className="h-4 w-4" />
					</button>
				</div>
			</div>
		</section>
	);
};
