import epic from "../../../public/assets/images/epic.svg.png";
import oscar from "../../../public/assets/images/oscar.png";
import accuro from "../../../public/assets/images/accuro.png";
import cerner from "../../../public/assets/images/cerner.png";
import oracle from "../../../public/assets/images/oracle.png";
import open from "../../../public/assets/images/open-emr.png";
import juno from "../../../public/assets/images/juno.png";

const integrationLogos = [
	{ src: epic.src, alt: "Epic" },
	{ src: oscar.src, alt: "OSCAR Pro" },
	{ src: accuro.src, alt: "Accuro" },
	{ src: cerner.src, alt: "Cerner" },
	{ src: oracle.src, alt: "Oracle Cerner" },
	{ src: open.src, alt: "openEMR" },
	{ src: juno.src, alt: "Juno" },
];

export const Interactions = () => {
	return (
		<section
			id="integrations"
			className="mb-[24px] flex w-full flex-col items-center justify-center px-4 pb-5 pt-[52px] font-inter lg:mb-[47px] lg:pb-[96px] lg:pt-[59px]"
		>
			<div className="mx-auto flex w-full max-w-4xl flex-col items-center text-center msm:max-w-[312px]">
				<span className="mb-2 font-shantell text-sm font-medium text-[#01B18B] lg:text-lg">
					Integrations
				</span>
				<h2 className="mb-2 text-xl font-bold text-[#0C0D0E] lg:text-[32px]">
					Seamless Integration with Your EMR and Tools{" "}
				</h2>
				<p className="mb-[52px] text-sm text-[#68778D] lg:text-lg">
					Quick setup, zero downtime, and smooth integration with your
					existing systems.
				</p>
			</div>
			<div className="mx-auto flex w-full max-w-6xl flex-wrap items-center justify-center gap-[31px] lg:gap-[34px] msm:max-w-[312px]">
				{integrationLogos.map((logo, idx) => (
					<img
						key={idx}
						src={logo.src}
						alt={logo.alt}
						className="h-7 w-auto"
					/>
				))}
			</div>
		</section>
	);
};
