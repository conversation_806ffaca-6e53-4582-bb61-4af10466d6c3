import { motion } from "framer-motion";

export const Partners = () => {
	const partnerLogos = [
		{ src: "/assets/images/OH-logo.svg", alt: "Ontario Health" },
		{
			src: "/assets/images/UOW.svg",
			alt: "University of Waterloo",
		},
		{
			src: "/assets/images/NM.svg",
			alt: "Network Medicals",
		},
		{
			src: "/assets/images/NWT.svg",
			alt: "North Western Toronto",
		},
		{ src: "/assets/images/microsoft.svg", alt: "Microsoft" },
		{ src: "/assets/images/velocity.png", alt: "Velocity" },
	];

	return (
		<section className="mt-[30px] w-full overflow-hidden py-[52px] lg:mt-9 lg:py-12">
			<motion.div
				className="flex gap-[24px] whitespace-nowrap lg:gap-[52.96px]"
				initial={{ x: "-50%" }}
				animate={{
					x: ["-50%", "-350%"],
				}}
				transition={{
					repeat: Infinity,
					duration: 55,
					ease: "linear",
				}}
			>
				{partnerLogos.map((logo, idx) => (
					<img
						key={`first-${idx}`}
						src={logo.src}
						alt={logo.alt}
						className="h-8 w-auto flex-shrink-0 grayscale"
						style={{ maxWidth: 140 }}
					/>
				))}
				<div className="w-[20vw] flex-shrink-0" />

				{partnerLogos.map((logo, idx) => (
					<img
						key={`second-${idx}`}
						src={logo.src}
						alt={logo.alt}
						className="h-8 w-auto flex-shrink-0 grayscale"
						style={{ maxWidth: 140 }}
					/>
				))}
				<div className="w-[20vw] flex-shrink-0" />

				{partnerLogos.map((logo, idx) => (
					<img
						key={`third-${idx}`}
						src={logo.src}
						alt={logo.alt}
						className="h-8 w-auto flex-shrink-0 grayscale"
						style={{ maxWidth: 140 }}
					/>
				))}
				<div className="w-[20vw] flex-shrink-0" />

				{partnerLogos.map((logo, idx) => (
					<img
						key={`fourth-${idx}`}
						src={logo.src}
						alt={logo.alt}
						className="h-8 w-auto flex-shrink-0 grayscale"
						style={{ maxWidth: 140 }}
					/>
				))}
			</motion.div>
		</section>
	);
};
